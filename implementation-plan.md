# INAT-4129 Implementation Plan: Incident Cause Analysis & Risk Matrix Mapping

## 1. Create New Mapping Entities

### 1.1 Incident Cause Analysis Mapping Entity

```typescript
// src/modules-qa/incident-investigation/entity/incident-cause-analysis-mapping.entity.ts
import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm';
import { IdentifyEntity } from 'svm-nest-lib-v3';
import { IncidentInvestigation } from './incident-investigation.entity';
import { CauseAnalysisMaster } from '../../../modules/cause-analysis-master/entities/cause-analysis-master.entity';
import { CauseMainCategory } from '../../../modules/cause-analysis-master/entities/cause-main-category.entity';
import { CauseSubCategory } from '../../../modules/cause-analysis-master/entities/cause-sub-category.entity';

@Entity()
@Index(['incidentInvestigationId', 'causeAnalysisMasterId'], { unique: true })
export class IncidentCauseAnalysisMapping extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'uuid' })
  public incidentInvestigationId: string;

  @Column({ type: 'uuid' })
  public causeAnalysisMasterId: string;

  @Column({ type: 'uuid', nullable: true })
  public causeMainCategoryId: string;

  @Column({ type: 'uuid', nullable: true })
  public causeSubCategoryId: string;

  @Column({ type: 'text', nullable: true })
  public analysisNotes: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  public mappingType: string; // 'immediate', 'underlying', 'root'

  // Relationships
  @ManyToOne(() => IncidentInvestigation, { onDelete: 'CASCADE' })
  incidentInvestigation: IncidentInvestigation;

  @ManyToOne(() => CauseAnalysisMaster, { onDelete: 'CASCADE' })
  causeAnalysisMaster: CauseAnalysisMaster;

  @ManyToOne(() => CauseMainCategory, { onDelete: 'SET NULL' })
  causeMainCategory: CauseMainCategory;

  @ManyToOne(() => CauseSubCategory, { onDelete: 'SET NULL' })
  causeSubCategory: CauseSubCategory;
}
```

### 1.2 Incident Risk Matrix Mapping Entity

```typescript
// src/modules-qa/incident-investigation/entity/incident-risk-matrix-mapping.entity.ts
import { Entity, Column, PrimaryGeneratedColumn, ManyToOne, Index } from 'typeorm';
import { IdentifyEntity } from 'svm-nest-lib-v3';
import { IncidentInvestigation } from './incident-investigation.entity';
import { RiskMatrixMaster } from '../../../modules/risk-matrix-master/entities/risk-matrix-master.entity';
import { RiskValueMapping } from '../../../modules/risk-matrix-master/entities/risk-value-mapping.entity';
import { PriorityMaster } from '../../../modules/priority-master/priority-master.entity';

@Entity()
@Index(['incidentInvestigationId', 'riskMatrixId'], { unique: true })
export class IncidentRiskMatrixMapping extends IdentifyEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'uuid' })
  public incidentInvestigationId: string;

  @Column({ type: 'uuid' })
  public riskMatrixId: string;

  @Column({ type: 'varchar', length: 10, nullable: true })
  public selectedCellPosition: string; // e.g., 'r2c3'

  @Column({ type: 'uuid', nullable: true })
  public riskValueMappingId: string;

  @Column({ type: 'uuid', nullable: true })
  public priorityMasterId: string;

  @Column({ type: 'int', nullable: true })
  public likelihoodScore: number;

  @Column({ type: 'int', nullable: true })
  public impactScore: number;

  @Column({ type: 'int', nullable: true })
  public overallRiskScore: number;

  @Column({ type: 'text', nullable: true })
  public riskAssessmentNotes: string;

  // Relationships
  @ManyToOne(() => IncidentInvestigation, { onDelete: 'CASCADE' })
  incidentInvestigation: IncidentInvestigation;

  @ManyToOne(() => RiskMatrixMaster, { onDelete: 'CASCADE' })
  riskMatrix: RiskMatrixMaster;

  @ManyToOne(() => RiskValueMapping, { onDelete: 'SET NULL' })
  riskValueMapping: RiskValueMapping;

  @ManyToOne(() => PriorityMaster, { onDelete: 'SET NULL' })
  priorityMaster: PriorityMaster;
}
```

## 2. Update Incident Investigation Entity

```typescript
// Add to src/modules-qa/incident-investigation/entity/incident-investigation.entity.ts
@OneToMany(() => IncidentCauseAnalysisMapping, (mapping) => mapping.incidentInvestigation)
causeAnalysisMappings: IncidentCauseAnalysisMapping[];

@OneToMany(() => IncidentRiskMatrixMapping, (mapping) => mapping.incidentInvestigation)
riskMatrixMappings: IncidentRiskMatrixMapping[];
```

## 3. Create DTOs for Mapping Operations

### 3.1 Cause Analysis Mapping DTOs

```typescript
// src/modules-qa/incident-investigation/dto/create-incident-cause-analysis-mapping.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsUUID, IsString, MaxLength, IsEnum } from 'class-validator';

export enum CauseMappingTypeEnum {
  IMMEDIATE = 'immediate',
  UNDERLYING = 'underlying', 
  ROOT = 'root'
}

export class CreateIncidentCauseAnalysisMappingDto {
  @ApiProperty({ description: 'Cause Analysis Master ID' })
  @IsNotEmpty()
  @IsUUID()
  causeAnalysisMasterId: string;

  @ApiProperty({ description: 'Cause Main Category ID', required: false })
  @IsOptional()
  @IsUUID()
  causeMainCategoryId?: string;

  @ApiProperty({ description: 'Cause Sub Category ID', required: false })
  @IsOptional()
  @IsUUID()
  causeSubCategoryId?: string;

  @ApiProperty({ description: 'Analysis notes', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(2000)
  analysisNotes?: string;

  @ApiProperty({ enum: CauseMappingTypeEnum, description: 'Type of cause mapping' })
  @IsNotEmpty()
  @IsEnum(CauseMappingTypeEnum)
  mappingType: string;
}
```

### 3.2 Risk Matrix Mapping DTOs

```typescript
// src/modules-qa/incident-investigation/dto/create-incident-risk-matrix-mapping.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsUUID, IsString, IsInt, Min, Max } from 'class-validator';

export class CreateIncidentRiskMatrixMappingDto {
  @ApiProperty({ description: 'Risk Matrix ID' })
  @IsNotEmpty()
  @IsUUID()
  riskMatrixId: string;

  @ApiProperty({ description: 'Selected cell position (e.g., r2c3)', required: false })
  @IsOptional()
  @IsString()
  selectedCellPosition?: string;

  @ApiProperty({ description: 'Risk Value Mapping ID', required: false })
  @IsOptional()
  @IsUUID()
  riskValueMappingId?: string;

  @ApiProperty({ description: 'Priority Master ID', required: false })
  @IsOptional()
  @IsUUID()
  priorityMasterId?: string;

  @ApiProperty({ description: 'Likelihood score (1-5)', required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(5)
  likelihoodScore?: number;

  @ApiProperty({ description: 'Impact score (1-5)', required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(5)
  impactScore?: number;

  @ApiProperty({ description: 'Overall risk score', required: false })
  @IsOptional()
  @IsInt()
  overallRiskScore?: number;

  @ApiProperty({ description: 'Risk assessment notes', required: false })
  @IsOptional()
  @IsString()
  riskAssessmentNotes?: string;
}
```

## 4. Create Repository Classes

### 4.1 Incident Cause Analysis Mapping Repository

```typescript
// src/modules-qa/incident-investigation/repository/incident-cause-analysis-mapping.repository.ts
import { EntityRepository, Connection } from 'typeorm';
import { TypeORMRepository, TokenPayloadModel } from 'svm-nest-lib-v3';
import { IncidentCauseAnalysisMapping } from '../entity/incident-cause-analysis-mapping.entity';
import { CreateIncidentCauseAnalysisMappingDto } from '../dto/create-incident-cause-analysis-mapping.dto';

@EntityRepository(IncidentCauseAnalysisMapping)
export class IncidentCauseAnalysisMappingRepository extends TypeORMRepository<IncidentCauseAnalysisMapping> {
  constructor(private readonly connection: Connection) {
    super();
  }

  async createMapping(
    incidentId: string,
    mappingDto: CreateIncidentCauseAnalysisMappingDto,
    token: TokenPayloadModel
  ): Promise<IncidentCauseAnalysisMapping> {
    const mapping = this.create({
      ...mappingDto,
      incidentInvestigationId: incidentId,
    });
    
    return await this.save(mapping);
  }

  async getMappingsByIncidentId(incidentId: string): Promise<IncidentCauseAnalysisMapping[]> {
    return await this.find({
      where: { incidentInvestigationId: incidentId, deleted: false },
      relations: ['causeAnalysisMaster', 'causeMainCategory', 'causeSubCategory']
    });
  }

  async updateMapping(
    id: string,
    mappingDto: Partial<CreateIncidentCauseAnalysisMappingDto>
  ): Promise<IncidentCauseAnalysisMapping> {
    await this.update(id, mappingDto);
    return await this.findOne(id);
  }

  async deleteMapping(id: string): Promise<void> {
    await this.update(id, { deleted: true });
  }
}
```

### 5.2 Incident Risk Matrix Mapping Service

```typescript
// src/modules-qa/incident-investigation/service/incident-risk-matrix-mapping.service.ts
import { Injectable } from '@nestjs/common';
import { TokenPayloadModel } from 'svm-nest-lib-v3';
import { IncidentRiskMatrixMappingRepository } from '../repository/incident-risk-matrix-mapping.repository';
import { CreateIncidentRiskMatrixMappingDto } from '../dto/create-incident-risk-matrix-mapping.dto';

@Injectable()
export class IncidentRiskMatrixMappingService {
  constructor(
    private readonly mappingRepository: IncidentRiskMatrixMappingRepository
  ) {}

  async createMapping(
    incidentId: string,
    mappingDto: CreateIncidentRiskMatrixMappingDto,
    token: TokenPayloadModel
  ) {
    return await this.mappingRepository.createMapping(incidentId, mappingDto, token);
  }

  async getMappingsByIncidentId(incidentId: string) {
    return await this.mappingRepository.getMappingsByIncidentId(incidentId);
  }

  async updateMapping(id: string, mappingDto: Partial<CreateIncidentRiskMatrixMappingDto>) {
    return await this.mappingRepository.updateMapping(id, mappingDto);
  }

  async deleteMapping(id: string) {
    return await this.mappingRepository.deleteMapping(id);
  }
}
```

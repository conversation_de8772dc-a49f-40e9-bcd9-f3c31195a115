# INAT-4129 Controller Endpoints Implementation

## 6. Add Controller Endpoints

### 6.1 Cause Analysis Mapping Endpoints

```typescript
// Add to src/modules-qa/incident-investigation/incident-investigation.controller.ts

// Import new services and DTOs
import { IncidentCauseAnalysisMappingService } from './service/incident-cause-analysis-mapping.service';
import { IncidentRiskMatrixMappingService } from './service/incident-risk-matrix-mapping.service';
import { CreateIncidentCauseAnalysisMappingDto } from './dto/create-incident-cause-analysis-mapping.dto';
import { CreateIncidentRiskMatrixMappingDto } from './dto/create-incident-risk-matrix-mapping.dto';

// Add to constructor
constructor(
  private readonly incidentInvestigationService: IncidentInvestigationService,
  private readonly causeAnalysisMappingService: IncidentCauseAnalysisMappingService,
  private readonly riskMatrixMappingService: IncidentRiskMatrixMappingService,
) {}

// Cause Analysis Mapping Endpoints
@ApiOperation({ summary: 'Create cause analysis mapping for incident' })
@ApiResponse({ description: 'Create cause analysis mapping success', status: HttpStatus.OK })
@ApiParam({ name: 'id', description: 'Incident Investigation ID' })
@ApiBody({ type: CreateIncidentCauseAnalysisMappingDto })
@Post('/:id/cause-analysis-mapping')
@Roles(RoleScope.ADMIN, RoleScope.USER)
async createCauseAnalysisMapping(
  @Param('id', ParseUUIDPipe) incidentId: string,
  @Body() body: CreateIncidentCauseAnalysisMappingDto,
  @TokenDecorator() token: TokenPayloadModel,
  @I18n() i18n: I18nContext,
) {
  await this.causeAnalysisMappingService.createMapping(incidentId, body, token);
  return {
    message: await i18n.t('common.CREATE_SUCCESS'),
  };
}

@ApiOperation({ summary: 'Get cause analysis mappings for incident' })
@ApiResponse({ description: 'Get cause analysis mappings success', status: HttpStatus.OK })
@ApiParam({ name: 'id', description: 'Incident Investigation ID' })
@Get('/:id/cause-analysis-mapping')
@Roles(RoleScope.ADMIN, RoleScope.USER)
async getCauseAnalysisMappings(
  @Param('id', ParseUUIDPipe) incidentId: string,
) {
  return await this.causeAnalysisMappingService.getMappingsByIncidentId(incidentId);
}

@ApiOperation({ summary: 'Update cause analysis mapping' })
@ApiResponse({ description: 'Update cause analysis mapping success', status: HttpStatus.OK })
@ApiParam({ name: 'mappingId', description: 'Cause Analysis Mapping ID' })
@ApiBody({ type: CreateIncidentCauseAnalysisMappingDto })
@Put('/cause-analysis-mapping/:mappingId')
@Roles(RoleScope.ADMIN, RoleScope.USER)
async updateCauseAnalysisMapping(
  @Param('mappingId', ParseUUIDPipe) mappingId: string,
  @Body() body: Partial<CreateIncidentCauseAnalysisMappingDto>,
  @I18n() i18n: I18nContext,
) {
  await this.causeAnalysisMappingService.updateMapping(mappingId, body);
  return {
    message: await i18n.t('common.UPDATE_SUCCESS'),
  };
}

@ApiOperation({ summary: 'Delete cause analysis mapping' })
@ApiResponse({ description: 'Delete cause analysis mapping success', status: HttpStatus.OK })
@ApiParam({ name: 'mappingId', description: 'Cause Analysis Mapping ID' })
@Delete('/cause-analysis-mapping/:mappingId')
@Roles(RoleScope.ADMIN, RoleScope.USER)
async deleteCauseAnalysisMapping(
  @Param('mappingId', ParseUUIDPipe) mappingId: string,
  @I18n() i18n: I18nContext,
) {
  await this.causeAnalysisMappingService.deleteMapping(mappingId);
  return {
    message: await i18n.t('common.DELETE_SUCCESS'),
  };
}
```

### 6.2 Risk Matrix Mapping Endpoints

```typescript
// Risk Matrix Mapping Endpoints
@ApiOperation({ summary: 'Create risk matrix mapping for incident' })
@ApiResponse({ description: 'Create risk matrix mapping success', status: HttpStatus.OK })
@ApiParam({ name: 'id', description: 'Incident Investigation ID' })
@ApiBody({ type: CreateIncidentRiskMatrixMappingDto })
@Post('/:id/risk-matrix-mapping')
@Roles(RoleScope.ADMIN, RoleScope.USER)
async createRiskMatrixMapping(
  @Param('id', ParseUUIDPipe) incidentId: string,
  @Body() body: CreateIncidentRiskMatrixMappingDto,
  @TokenDecorator() token: TokenPayloadModel,
  @I18n() i18n: I18nContext,
) {
  await this.riskMatrixMappingService.createMapping(incidentId, body, token);
  return {
    message: await i18n.t('common.CREATE_SUCCESS'),
  };
}

@ApiOperation({ summary: 'Get risk matrix mappings for incident' })
@ApiResponse({ description: 'Get risk matrix mappings success', status: HttpStatus.OK })
@ApiParam({ name: 'id', description: 'Incident Investigation ID' })
@Get('/:id/risk-matrix-mapping')
@Roles(RoleScope.ADMIN, RoleScope.USER)
async getRiskMatrixMappings(
  @Param('id', ParseUUIDPipe) incidentId: string,
) {
  return await this.riskMatrixMappingService.getMappingsByIncidentId(incidentId);
}

@ApiOperation({ summary: 'Update risk matrix mapping' })
@ApiResponse({ description: 'Update risk matrix mapping success', status: HttpStatus.OK })
@ApiParam({ name: 'mappingId', description: 'Risk Matrix Mapping ID' })
@ApiBody({ type: CreateIncidentRiskMatrixMappingDto })
@Put('/risk-matrix-mapping/:mappingId')
@Roles(RoleScope.ADMIN, RoleScope.USER)
async updateRiskMatrixMapping(
  @Param('mappingId', ParseUUIDPipe) mappingId: string,
  @Body() body: Partial<CreateIncidentRiskMatrixMappingDto>,
  @I18n() i18n: I18nContext,
) {
  await this.riskMatrixMappingService.updateMapping(mappingId, body);
  return {
    message: await i18n.t('common.UPDATE_SUCCESS'),
  };
}

@ApiOperation({ summary: 'Delete risk matrix mapping' })
@ApiResponse({ description: 'Delete risk matrix mapping success', status: HttpStatus.OK })
@ApiParam({ name: 'mappingId', description: 'Risk Matrix Mapping ID' })
@Delete('/risk-matrix-mapping/:mappingId')
@Roles(RoleScope.ADMIN, RoleScope.USER)
async deleteRiskMatrixMapping(
  @Param('mappingId', ParseUUIDPipe) mappingId: string,
  @I18n() i18n: I18nContext,
) {
  await this.riskMatrixMappingService.deleteMapping(mappingId);
  return {
    message: await i18n.t('common.DELETE_SUCCESS'),
  };
}
```

### 6.3 Combined Endpoint for Getting All Mappings

```typescript
@ApiOperation({ summary: 'Get all cause analysis and risk matrix mappings for incident' })
@ApiResponse({ description: 'Get all mappings success', status: HttpStatus.OK })
@ApiParam({ name: 'id', description: 'Incident Investigation ID' })
@Get('/:id/all-mappings')
@Roles(RoleScope.ADMIN, RoleScope.USER)
async getAllMappings(
  @Param('id', ParseUUIDPipe) incidentId: string,
) {
  const [causeAnalysisMappings, riskMatrixMappings] = await Promise.all([
    this.causeAnalysisMappingService.getMappingsByIncidentId(incidentId),
    this.riskMatrixMappingService.getMappingsByIncidentId(incidentId),
  ]);

  return {
    causeAnalysisMappings,
    riskMatrixMappings,
  };
}
```

## 7. Update Module Configuration

```typescript
// Update src/modules-qa/incident-investigation/incident-investigation.module.ts
import { IncidentCauseAnalysisMappingRepository } from './repository/incident-cause-analysis-mapping.repository';
import { IncidentRiskMatrixMappingRepository } from './repository/incident-risk-matrix-mapping.repository';
import { IncidentCauseAnalysisMappingService } from './service/incident-cause-analysis-mapping.service';
import { IncidentRiskMatrixMappingService } from './service/incident-risk-matrix-mapping.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      IncidentInvestigationRepository,
      IncidentInvestigationRemarkRepository,
      IncidentInvestigationReviewRepository,
      IncidentInvestigationCommentRepository,
      IncidentInvestigationHistoryRepository,
      InjuryRepository,
      // Add new repositories
      IncidentCauseAnalysisMappingRepository,
      IncidentRiskMatrixMappingRepository,
    ]),
    MicroservicesAsyncModule,
    MicroservicesSyncModule,
  ],
  controllers: [IncidentInvestigationController],
  providers: [
    IncidentInvestigationService,
    // Add new services
    IncidentCauseAnalysisMappingService,
    IncidentRiskMatrixMappingService,
  ],
  exports: [],
})
export class IncidentInvestigationModule {}
```

## 8. Database Migration

```typescript
// Create migration file: src/migrations/[timestamp]-create-incident-mapping-tables.ts
import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateIncidentMappingTables[timestamp] implements MigrationInterface {
  name = 'CreateIncidentMappingTables[timestamp]'

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create incident_cause_analysis_mapping table
    await queryRunner.query(`
      CREATE TABLE "incident_cause_analysis_mapping" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "deleted" boolean NOT NULL DEFAULT false,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "incidentInvestigationId" uuid NOT NULL,
        "causeAnalysisMasterId" uuid NOT NULL,
        "causeMainCategoryId" uuid,
        "causeSubCategoryId" uuid,
        "analysisNotes" text,
        "mappingType" character varying(50),
        CONSTRAINT "PK_incident_cause_analysis_mapping" PRIMARY KEY ("id")
      )
    `);

    // Create incident_risk_matrix_mapping table
    await queryRunner.query(`
      CREATE TABLE "incident_risk_matrix_mapping" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "deleted" boolean NOT NULL DEFAULT false,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "incidentInvestigationId" uuid NOT NULL,
        "riskMatrixId" uuid NOT NULL,
        "selectedCellPosition" character varying(10),
        "riskValueMappingId" uuid,
        "priorityMasterId" uuid,
        "likelihoodScore" integer,
        "impactScore" integer,
        "overallRiskScore" integer,
        "riskAssessmentNotes" text,
        CONSTRAINT "PK_incident_risk_matrix_mapping" PRIMARY KEY ("id")
      )
    `);

    // Create indexes
    await queryRunner.query(`
      CREATE UNIQUE INDEX "idx_incident_cause_analysis_mapping_unique" 
      ON "incident_cause_analysis_mapping" ("incidentInvestigationId", "causeAnalysisMasterId") 
      WHERE deleted = false
    `);

    await queryRunner.query(`
      CREATE UNIQUE INDEX "idx_incident_risk_matrix_mapping_unique" 
      ON "incident_risk_matrix_mapping" ("incidentInvestigationId", "riskMatrixId") 
      WHERE deleted = false
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "incident_cause_analysis_mapping" 
      ADD CONSTRAINT "FK_incident_cause_analysis_mapping_incident" 
      FOREIGN KEY ("incidentInvestigationId") 
      REFERENCES "incident_investigation"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "incident_risk_matrix_mapping" 
      ADD CONSTRAINT "FK_incident_risk_matrix_mapping_incident" 
      FOREIGN KEY ("incidentInvestigationId") 
      REFERENCES "incident_investigation"("id") ON DELETE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "incident_risk_matrix_mapping"`);
    await queryRunner.query(`DROP TABLE "incident_cause_analysis_mapping"`);
  }
}
```
